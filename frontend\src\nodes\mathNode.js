// mathNode.js
// Demonstrates mathematical operations with multiple inputs

import { useState } from 'react';
import { BaseNode, createHandle, commonLabelStyle } from './BaseNode';
import { Position } from 'reactflow';

export const MathNode = ({ id, data }) => {
  const [operation, setOperation] = useState(data?.operation || 'add');

  const handleOperationChange = (e) => {
    setOperation(e.target.value);
  };

  const handles = [
    createHandle(`${id}-input1`, 'target', Position.Left, { top: '25%' }),
    createHandle(`${id}-input2`, 'target', Position.Left, { top: '75%' }),
    createHandle(`${id}-result`, 'source', Position.Right)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Math"
      handles={handles}
      nodeType="math"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={commonLabelStyle}>
          Operation:
          <select
            value={operation}
            onChange={handleOperationChange}
            className="node-input"
          >
            <option value="add">Add</option>
            <option value="subtract">Subtract</option>
            <option value="multiply">Multiply</option>
            <option value="divide">Divide</option>
          </select>
        </label>
        <div style={{
          fontSize: '10px',
          color: 'rgba(255, 255, 255, 0.6)',
          fontStyle: 'italic',
          textAlign: 'center',
          padding: '4px 8px',
          backgroundColor: 'rgba(138, 43, 226, 0.1)',
          borderRadius: '4px',
          border: '1px solid rgba(138, 43, 226, 0.2)'
        }}>
          Performs {operation} operation
        </div>
      </div>
    </BaseNode>
  );
};
