{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\timerNode.js\",\n  _s = $RefreshSig$();\n// timerNode.js\n// Demonstrates a timer/delay node with configurable duration\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, inlineLabelStyle } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TimerNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [duration, setDuration] = useState((data === null || data === void 0 ? void 0 : data.duration) || 1000);\n  const [unit, setUnit] = useState((data === null || data === void 0 ? void 0 : data.unit) || 'ms');\n  const handleDurationChange = e => {\n    setDuration(parseInt(e.target.value) || 0);\n  };\n  const handleUnitChange = e => {\n    setUnit(e.target.value);\n  };\n  const handles = [HANDLE_CONFIGS.targetLeft(`${id}-trigger`), HANDLE_CONFIGS.sourceRight(`${id}-delayed`)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Timer\",\n    handles: handles,\n    nodeType: \"timer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px',\n          marginBottom: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: inlineLabelStyle,\n          children: [\"Duration:\", /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: duration,\n            onChange: handleDurationChange,\n            className: \"node-input\",\n            style: {\n              width: '80px',\n              marginLeft: '4px'\n            },\n            min: \"0\",\n            step: \"1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: inlineLabelStyle,\n          children: [\"Unit:\", /*#__PURE__*/_jsxDEV(\"select\", {\n            value: unit,\n            onChange: handleUnitChange,\n            className: \"node-input\",\n            style: {\n              width: '60px',\n              marginLeft: '4px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"ms\",\n              children: \"ms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"s\",\n              children: \"s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"m\",\n              children: \"m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center'\n        },\n        children: [\"Delays execution by \", duration, \" \", unit]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(TimerNode, \"m+3RnTue3OQ7QWY94W+/xWdhgNE=\");\n_c = TimerNode;\nvar _c;\n$RefreshReg$(_c, \"TimerNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "inlineLabelStyle", "jsxDEV", "_jsxDEV", "TimerNode", "id", "data", "_s", "duration", "setDuration", "unit", "setUnit", "handleDurationChange", "e", "parseInt", "target", "value", "handleUnitChange", "handles", "targetLeft", "sourceRight", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "alignItems", "marginBottom", "type", "onChange", "className", "width", "marginLeft", "min", "step", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "color", "fontStyle", "textAlign", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/timerNode.js"], "sourcesContent": ["// timerNode.js\n// Demonstrates a timer/delay node with configurable duration\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, inlineLabelStyle } from './BaseNode';\n\nexport const TimerNode = ({ id, data }) => {\n  const [duration, setDuration] = useState(data?.duration || 1000);\n  const [unit, setUnit] = useState(data?.unit || 'ms');\n\n  const handleDurationChange = (e) => {\n    setDuration(parseInt(e.target.value) || 0);\n  };\n\n  const handleUnitChange = (e) => {\n    setUnit(e.target.value);\n  };\n\n  const handles = [\n    HANDLE_CONFIGS.targetLeft(`${id}-trigger`),\n    HANDLE_CONFIGS.sourceRight(`${id}-delayed`)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Timer\"\n      handles={handles}\n      nodeType=\"timer\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        {/* Inline grouped Duration and Unit */}\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px',\n          marginBottom: '10px'\n        }}>\n          <label style={inlineLabelStyle}>\n            Duration:\n            <input\n              type=\"number\"\n              value={duration}\n              onChange={handleDurationChange}\n              className=\"node-input\"\n              style={{\n                width: '80px',\n                marginLeft: '4px'\n              }}\n              min=\"0\"\n              step=\"1\"\n            />\n          </label>\n          <label style={inlineLabelStyle}>\n            Unit:\n            <select\n              value={unit}\n              onChange={handleUnitChange}\n              className=\"node-input\"\n              style={{\n                width: '60px',\n                marginLeft: '4px'\n              }}\n            >\n              <option value=\"ms\">ms</option>\n              <option value=\"s\">s</option>\n              <option value=\"m\">m</option>\n            </select>\n          </label>\n        </div>\n\n        {/* Helper text */}\n        <div style={{\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center'\n        }}>\n          Delays execution by {duration} {unit}\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,QAAQ,KAAI,IAAI,CAAC;EAChE,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,IAAI,KAAI,IAAI,CAAC;EAEpD,MAAME,oBAAoB,GAAIC,CAAC,IAAK;IAClCJ,WAAW,CAACK,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,gBAAgB,GAAIJ,CAAC,IAAK;IAC9BF,OAAO,CAACE,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;EACzB,CAAC;EAED,MAAME,OAAO,GAAG,CACdlB,cAAc,CAACmB,UAAU,CAAE,GAAEd,EAAG,UAAS,CAAC,EAC1CL,cAAc,CAACoB,WAAW,CAAE,GAAEf,EAAG,UAAS,CAAC,CAC5C;EAED,oBACEF,OAAA,CAACJ,QAAQ;IACPM,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAC,OAAO;IACbH,OAAO,EAAEA,OAAQ;IACjBI,QAAQ,EAAC,OAAO;IAAAC,QAAA,eAEhBpB,OAAA;MAAKqB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBAEnEpB,OAAA;QAAKqB,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfG,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,MAAM;UACXE,YAAY,EAAE;QAChB,CAAE;QAAAN,QAAA,gBACApB,OAAA;UAAOqB,KAAK,EAAEvB,gBAAiB;UAAAsB,QAAA,GAAC,WAE9B,eAAApB,OAAA;YACE2B,IAAI,EAAC,QAAQ;YACbd,KAAK,EAAER,QAAS;YAChBuB,QAAQ,EAAEnB,oBAAqB;YAC/BoB,SAAS,EAAC,YAAY;YACtBR,KAAK,EAAE;cACLS,KAAK,EAAE,MAAM;cACbC,UAAU,EAAE;YACd,CAAE;YACFC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACRrC,OAAA;UAAOqB,KAAK,EAAEvB,gBAAiB;UAAAsB,QAAA,GAAC,OAE9B,eAAApB,OAAA;YACEa,KAAK,EAAEN,IAAK;YACZqB,QAAQ,EAAEd,gBAAiB;YAC3Be,SAAS,EAAC,YAAY;YACtBR,KAAK,EAAE;cACLS,KAAK,EAAE,MAAM;cACbC,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,gBAEFpB,OAAA;cAAQa,KAAK,EAAC,IAAI;cAAAO,QAAA,EAAC;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BrC,OAAA;cAAQa,KAAK,EAAC,GAAG;cAAAO,QAAA,EAAC;YAAC;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5BrC,OAAA;cAAQa,KAAK,EAAC,GAAG;cAAAO,QAAA,EAAC;YAAC;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrC,OAAA;QAAKqB,KAAK,EAAE;UACViB,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,0BAA0B;UACjCC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE;QACb,CAAE;QAAArB,QAAA,GAAC,sBACmB,EAACf,QAAQ,EAAC,GAAC,EAACE,IAAI;MAAA;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACjC,EAAA,CA9EWH,SAAS;AAAAyC,EAAA,GAATzC,SAAS;AAAA,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}