{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\components\\\\NodeSidebar.js\",\n  _s = $RefreshSig$();\n// NodeSidebar.js\n// Vertical left sidebar for draggable nodes with responsive design\n\nimport { useState, useEffect } from 'react';\nimport { Download, Upload, Brain, Type, Calculator, Filter, Clock, GitBranch, Layers, Menu, X } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst nodeTypes = [{\n  type: 'customInput',\n  label: 'Input',\n  icon: Download,\n  color: '#8b5cf6',\n  description: 'Data input source'\n}, {\n  type: 'llm',\n  label: 'LLM',\n  icon: Brain,\n  color: '#6366f1',\n  description: 'Language model processing'\n}, {\n  type: 'customOutput',\n  label: 'Output',\n  icon: Upload,\n  color: '#8b5cf6',\n  description: 'Data output destination'\n}, {\n  type: 'text',\n  label: 'Text',\n  icon: Type,\n  color: '#10b981',\n  description: 'Text processing and templates'\n}, {\n  type: 'math',\n  label: 'Math',\n  icon: Calculator,\n  color: '#3b82f6',\n  description: 'Mathematical operations'\n}, {\n  type: 'filter',\n  label: 'Filter',\n  icon: Filter,\n  color: '#f59e0b',\n  description: 'Data filtering and conditions'\n}, {\n  type: 'timer',\n  label: 'Timer',\n  icon: Clock,\n  color: '#ef4444',\n  description: 'Delay and timing control'\n}, {\n  type: 'switch',\n  label: 'Switch',\n  icon: GitBranch,\n  color: '#06b6d4',\n  description: 'Conditional routing'\n}, {\n  type: 'aggregator',\n  label: 'Aggregator',\n  icon: Layers,\n  color: '#ec4899',\n  description: 'Data aggregation and combination'\n}];\nexport const NodeSidebar = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(window.innerWidth >= 1024);\n  const [draggedNode, setDraggedNode] = useState(null);\n  const onDragStart = (event, nodeType) => {\n    const appData = {\n      nodeType\n    };\n    event.target.style.cursor = 'grabbing';\n    event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n    event.dataTransfer.effectAllowed = 'move';\n    setDraggedNode(nodeType);\n\n    // Create drag preview\n    const dragPreview = event.target.cloneNode(true);\n    dragPreview.style.opacity = '0.7';\n    dragPreview.style.transform = 'scale(0.9)';\n    dragPreview.style.position = 'absolute';\n    dragPreview.style.top = '-1000px';\n    document.body.appendChild(dragPreview);\n    event.dataTransfer.setDragImage(dragPreview, 50, 25);\n    setTimeout(() => {\n      document.body.removeChild(dragPreview);\n    }, 0);\n  };\n  const onDragEnd = event => {\n    event.target.style.cursor = 'grab';\n    setDraggedNode(null);\n  };\n  const toggleSidebar = () => {\n    setIsOpen(!isOpen);\n  };\n\n  // Handle responsive behavior\n  useEffect(() => {\n    const handleResize = () => {\n      if (window.innerWidth >= 1024) {\n        setIsOpen(true);\n      } else if (window.innerWidth < 768) {\n        setIsOpen(false);\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"sidebar-toggle\",\n      onClick: toggleSidebar,\n      \"aria-label\": \"Toggle node sidebar\",\n      children: isOpen ? /*#__PURE__*/_jsxDEV(X, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 19\n      }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 37\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `node-sidebar ${isOpen ? 'open' : 'closed'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-logo\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 32 32\",\n            fill: \"none\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M16 2L30 9v14L16 30 2 23V9L16 2z\",\n              fill: \"url(#gradient)\",\n              stroke: \"#8b5cf6\",\n              strokeWidth: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n              children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                id: \"gradient\",\n                x1: \"0%\",\n                y1: \"0%\",\n                x2: \"100%\",\n                y2: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"0%\",\n                  stopColor: \"#8b5cf6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"100%\",\n                  stopColor: \"#6366f1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"sidebar-title\",\n          children: \"Nodes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-close-btn\",\n          onClick: toggleSidebar,\n          \"aria-label\": \"Close sidebar\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-content\",\n        children: nodeTypes.map(node => {\n          const IconComponent = node.icon;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `sidebar-node ${draggedNode === node.type ? 'dragging' : ''}`,\n            draggable: true,\n            onDragStart: event => onDragStart(event, node.type),\n            onDragEnd: onDragEnd,\n            style: {\n              '--node-color': node.color,\n              cursor: 'grab'\n            },\n            title: node.description,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"node-icon\",\n              children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                size: 18\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"node-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"node-label\",\n                children: node.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"node-description\",\n                children: node.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)]\n          }, node.type, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-overlay md:hidden\",\n      onClick: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(NodeSidebar, \"ehRlBiUX/gcxNBBYf374tXmFzfI=\");\n_c = NodeSidebar;\nvar _c;\n$RefreshReg$(_c, \"NodeSidebar\");", "map": {"version": 3, "names": ["useState", "useEffect", "Download", "Upload", "Brain", "Type", "Calculator", "Filter", "Clock", "GitBranch", "Layers", "<PERSON><PERSON>", "X", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "nodeTypes", "type", "label", "icon", "color", "description", "NodeSidebar", "_s", "isOpen", "setIsOpen", "window", "innerWidth", "draggedNode", "setDraggedNode", "onDragStart", "event", "nodeType", "appData", "target", "style", "cursor", "dataTransfer", "setData", "JSON", "stringify", "effectAllowed", "dragPreview", "cloneNode", "opacity", "transform", "position", "top", "document", "body", "append<PERSON><PERSON><PERSON>", "setDragImage", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "onDragEnd", "toggleSidebar", "handleResize", "addEventListener", "removeEventListener", "children", "className", "onClick", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "map", "node", "IconComponent", "draggable", "title", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/components/NodeSidebar.js"], "sourcesContent": ["// NodeSidebar.js\n// Vertical left sidebar for draggable nodes with responsive design\n\nimport { useState, useEffect } from 'react';\nimport { \n  Download, \n  Upload, \n  Brain, \n  Type, \n  Calculator, \n  Filter, \n  Clock, \n  GitBranch, \n  Layers,\n  Menu,\n  X\n} from 'lucide-react';\n\nconst nodeTypes = [\n  { \n    type: 'customInput', \n    label: 'Input', \n    icon: Download, \n    color: '#8b5cf6',\n    description: 'Data input source'\n  },\n  { \n    type: 'llm', \n    label: 'LLM', \n    icon: Brain, \n    color: '#6366f1',\n    description: 'Language model processing'\n  },\n  { \n    type: 'customOutput', \n    label: 'Output', \n    icon: Upload, \n    color: '#8b5cf6',\n    description: 'Data output destination'\n  },\n  { \n    type: 'text', \n    label: 'Text', \n    icon: Type, \n    color: '#10b981',\n    description: 'Text processing and templates'\n  },\n  { \n    type: 'math', \n    label: 'Math', \n    icon: Calculator, \n    color: '#3b82f6',\n    description: 'Mathematical operations'\n  },\n  { \n    type: 'filter', \n    label: 'Filter', \n    icon: Filter, \n    color: '#f59e0b',\n    description: 'Data filtering and conditions'\n  },\n  { \n    type: 'timer', \n    label: 'Timer', \n    icon: Clock, \n    color: '#ef4444',\n    description: 'Delay and timing control'\n  },\n  { \n    type: 'switch', \n    label: 'Switch', \n    icon: GitBranch, \n    color: '#06b6d4',\n    description: 'Conditional routing'\n  },\n  { \n    type: 'aggregator', \n    label: 'Aggregator', \n    icon: Layers, \n    color: '#ec4899',\n    description: 'Data aggregation and combination'\n  }\n];\n\nexport const NodeSidebar = () => {\n  const [isOpen, setIsOpen] = useState(window.innerWidth >= 1024);\n  const [draggedNode, setDraggedNode] = useState(null);\n\n  const onDragStart = (event, nodeType) => {\n    const appData = { nodeType };\n    event.target.style.cursor = 'grabbing';\n    event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n    event.dataTransfer.effectAllowed = 'move';\n    setDraggedNode(nodeType);\n    \n    // Create drag preview\n    const dragPreview = event.target.cloneNode(true);\n    dragPreview.style.opacity = '0.7';\n    dragPreview.style.transform = 'scale(0.9)';\n    dragPreview.style.position = 'absolute';\n    dragPreview.style.top = '-1000px';\n    document.body.appendChild(dragPreview);\n    event.dataTransfer.setDragImage(dragPreview, 50, 25);\n    \n    setTimeout(() => {\n      document.body.removeChild(dragPreview);\n    }, 0);\n  };\n\n  const onDragEnd = (event) => {\n    event.target.style.cursor = 'grab';\n    setDraggedNode(null);\n  };\n\n  const toggleSidebar = () => {\n    setIsOpen(!isOpen);\n  };\n\n  // Handle responsive behavior\n  useEffect(() => {\n    const handleResize = () => {\n      if (window.innerWidth >= 1024) {\n        setIsOpen(true);\n      } else if (window.innerWidth < 768) {\n        setIsOpen(false);\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  return (\n    <>\n      {/* Hamburger Button - Responsive */}\n      <button\n        className=\"sidebar-toggle\"\n        onClick={toggleSidebar}\n        aria-label=\"Toggle node sidebar\"\n      >\n        {isOpen ? <X size={20} /> : <Menu size={20} />}\n      </button>\n\n      {/* Sidebar */}\n      <div className={`node-sidebar ${isOpen ? 'open' : 'closed'}`}>\n        {/* Header */}\n        <div className=\"sidebar-header\">\n          <div className=\"sidebar-logo\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"none\">\n              <path d=\"M16 2L30 9v14L16 30 2 23V9L16 2z\" fill=\"url(#gradient)\" stroke=\"#8b5cf6\" strokeWidth=\"1\"/>\n              <defs>\n                <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                  <stop offset=\"0%\" stopColor=\"#8b5cf6\"/>\n                  <stop offset=\"100%\" stopColor=\"#6366f1\"/>\n                </linearGradient>\n              </defs>\n            </svg>\n          </div>\n          <h2 className=\"sidebar-title\">Nodes</h2>\n          <button\n            className=\"sidebar-close-btn\"\n            onClick={toggleSidebar}\n            aria-label=\"Close sidebar\"\n          >\n            <X size={16} />\n          </button>\n        </div>\n\n        {/* Node List */}\n        <div className=\"sidebar-content\">\n          {nodeTypes.map((node) => {\n            const IconComponent = node.icon;\n            return (\n              <div\n                key={node.type}\n                className={`sidebar-node ${draggedNode === node.type ? 'dragging' : ''}`}\n                draggable\n                onDragStart={(event) => onDragStart(event, node.type)}\n                onDragEnd={onDragEnd}\n                style={{\n                  '--node-color': node.color,\n                  cursor: 'grab'\n                }}\n                title={node.description}\n              >\n                <div className=\"node-icon\">\n                  <IconComponent size={18} />\n                </div>\n                <div className=\"node-info\">\n                  <span className=\"node-label\">{node.label}</span>\n                  <span className=\"node-description\">{node.description}</span>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Overlay for mobile */}\n      {isOpen && (\n        <div \n          className=\"sidebar-overlay md:hidden\"\n          onClick={toggleSidebar}\n        />\n      )}\n    </>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,CAAC,QACI,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,SAAS,GAAG,CAChB;EACEC,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAElB,QAAQ;EACdmB,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEJ,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,KAAK;EACZC,IAAI,EAAEhB,KAAK;EACXiB,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEJ,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAEjB,MAAM;EACZkB,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEJ,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAEf,IAAI;EACVgB,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEJ,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAEd,UAAU;EAChBe,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAEb,MAAM;EACZc,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEJ,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAEZ,KAAK;EACXa,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAEX,SAAS;EACfY,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEJ,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAEV,MAAM;EACZW,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,CACF;AAED,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC2B,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;EAC/D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAEpD,MAAM+B,WAAW,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IACvC,MAAMC,OAAO,GAAG;MAAED;IAAS,CAAC;IAC5BD,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,UAAU;IACtCL,KAAK,CAACM,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAACP,OAAO,CAAC,CAAC;IAC5EF,KAAK,CAACM,YAAY,CAACI,aAAa,GAAG,MAAM;IACzCZ,cAAc,CAACG,QAAQ,CAAC;;IAExB;IACA,MAAMU,WAAW,GAAGX,KAAK,CAACG,MAAM,CAACS,SAAS,CAAC,IAAI,CAAC;IAChDD,WAAW,CAACP,KAAK,CAACS,OAAO,GAAG,KAAK;IACjCF,WAAW,CAACP,KAAK,CAACU,SAAS,GAAG,YAAY;IAC1CH,WAAW,CAACP,KAAK,CAACW,QAAQ,GAAG,UAAU;IACvCJ,WAAW,CAACP,KAAK,CAACY,GAAG,GAAG,SAAS;IACjCC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACR,WAAW,CAAC;IACtCX,KAAK,CAACM,YAAY,CAACc,YAAY,CAACT,WAAW,EAAE,EAAE,EAAE,EAAE,CAAC;IAEpDU,UAAU,CAAC,MAAM;MACfJ,QAAQ,CAACC,IAAI,CAACI,WAAW,CAACX,WAAW,CAAC;IACxC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAED,MAAMY,SAAS,GAAIvB,KAAK,IAAK;IAC3BA,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IAClCP,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B9B,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;;EAED;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMwD,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI9B,MAAM,CAACC,UAAU,IAAI,IAAI,EAAE;QAC7BF,SAAS,CAAC,IAAI,CAAC;MACjB,CAAC,MAAM,IAAIC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;QAClCF,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDC,MAAM,CAAC+B,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAM9B,MAAM,CAACgC,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE3C,OAAA,CAAAE,SAAA;IAAA4C,QAAA,gBAEE9C,OAAA;MACE+C,SAAS,EAAC,gBAAgB;MAC1BC,OAAO,EAAEN,aAAc;MACvB,cAAW,qBAAqB;MAAAI,QAAA,EAE/BnC,MAAM,gBAAGX,OAAA,CAACF,CAAC;QAACmD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGrD,OAAA,CAACH,IAAI;QAACoD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eAGTrD,OAAA;MAAK+C,SAAS,EAAG,gBAAepC,MAAM,GAAG,MAAM,GAAG,QAAS,EAAE;MAAAmC,QAAA,gBAE3D9C,OAAA;QAAK+C,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7B9C,OAAA;UAAK+C,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC3B9C,OAAA;YAAKsD,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAX,QAAA,gBACzD9C,OAAA;cAAM0D,CAAC,EAAC,kCAAkC;cAACD,IAAI,EAAC,gBAAgB;cAACE,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACnGrD,OAAA;cAAA8C,QAAA,eACE9C,OAAA;gBAAgB6D,EAAE,EAAC,UAAU;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,MAAM;gBAAAnB,QAAA,gBAC/D9C,OAAA;kBAAMkE,MAAM,EAAC,IAAI;kBAACC,SAAS,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACvCrD,OAAA;kBAAMkE,MAAM,EAAC,MAAM;kBAACC,SAAS,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrD,OAAA;UAAI+C,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxCrD,OAAA;UACE+C,SAAS,EAAC,mBAAmB;UAC7BC,OAAO,EAAEN,aAAc;UACvB,cAAW,eAAe;UAAAI,QAAA,eAE1B9C,OAAA,CAACF,CAAC;YAACmD,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNrD,OAAA;QAAK+C,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAC7B3C,SAAS,CAACiE,GAAG,CAAEC,IAAI,IAAK;UACvB,MAAMC,aAAa,GAAGD,IAAI,CAAC/D,IAAI;UAC/B,oBACEN,OAAA;YAEE+C,SAAS,EAAG,gBAAehC,WAAW,KAAKsD,IAAI,CAACjE,IAAI,GAAG,UAAU,GAAG,EAAG,EAAE;YACzEmE,SAAS;YACTtD,WAAW,EAAGC,KAAK,IAAKD,WAAW,CAACC,KAAK,EAAEmD,IAAI,CAACjE,IAAI,CAAE;YACtDqC,SAAS,EAAEA,SAAU;YACrBnB,KAAK,EAAE;cACL,cAAc,EAAE+C,IAAI,CAAC9D,KAAK;cAC1BgB,MAAM,EAAE;YACV,CAAE;YACFiD,KAAK,EAAEH,IAAI,CAAC7D,WAAY;YAAAsC,QAAA,gBAExB9C,OAAA;cAAK+C,SAAS,EAAC,WAAW;cAAAD,QAAA,eACxB9C,OAAA,CAACsE,aAAa;gBAACrB,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACNrD,OAAA;cAAK+C,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxB9C,OAAA;gBAAM+C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAEuB,IAAI,CAAChE;cAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChDrD,OAAA;gBAAM+C,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,EAAEuB,IAAI,CAAC7D;cAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA,GAjBDgB,IAAI,CAACjE,IAAI;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBX,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1C,MAAM,iBACLX,OAAA;MACE+C,SAAS,EAAC,2BAA2B;MACrCC,OAAO,EAAEN;IAAc;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF;EAAA,eACD,CAAC;AAEP,CAAC;AAAC3C,EAAA,CA3HWD,WAAW;AAAAgE,EAAA,GAAXhE,WAAW;AAAA,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}