// NodeSidebar.js
// Vertical left sidebar for draggable nodes with responsive design

import { useState, useEffect } from 'react';
import { 
  Download, 
  Upload, 
  Brain, 
  Type, 
  Calculator, 
  Filter, 
  Clock, 
  GitBranch, 
  Layers,
  Menu,
  X
} from 'lucide-react';

const nodeTypes = [
  { 
    type: 'customInput', 
    label: 'Input', 
    icon: Download, 
    color: '#8b5cf6',
    description: 'Data input source'
  },
  { 
    type: 'llm', 
    label: 'LLM', 
    icon: Brain, 
    color: '#6366f1',
    description: 'Language model processing'
  },
  { 
    type: 'customOutput', 
    label: 'Output', 
    icon: Upload, 
    color: '#8b5cf6',
    description: 'Data output destination'
  },
  { 
    type: 'text', 
    label: 'Text', 
    icon: Type, 
    color: '#10b981',
    description: 'Text processing and templates'
  },
  { 
    type: 'math', 
    label: 'Math', 
    icon: Calculator, 
    color: '#3b82f6',
    description: 'Mathematical operations'
  },
  { 
    type: 'filter', 
    label: 'Filter', 
    icon: Filter, 
    color: '#f59e0b',
    description: 'Data filtering and conditions'
  },
  { 
    type: 'timer', 
    label: 'Timer', 
    icon: Clock, 
    color: '#ef4444',
    description: 'Delay and timing control'
  },
  { 
    type: 'switch', 
    label: 'Switch', 
    icon: GitBranch, 
    color: '#06b6d4',
    description: 'Conditional routing'
  },
  { 
    type: 'aggregator', 
    label: 'Aggregator', 
    icon: Layers, 
    color: '#ec4899',
    description: 'Data aggregation and combination'
  }
];

export const NodeSidebar = () => {
  const [isOpen, setIsOpen] = useState(window.innerWidth >= 1024);
  const [draggedNode, setDraggedNode] = useState(null);

  const onDragStart = (event, nodeType) => {
    const appData = { nodeType };
    event.target.style.cursor = 'grabbing';
    event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));
    event.dataTransfer.effectAllowed = 'move';
    setDraggedNode(nodeType);
    
    // Create drag preview
    const dragPreview = event.target.cloneNode(true);
    dragPreview.style.opacity = '0.7';
    dragPreview.style.transform = 'scale(0.9)';
    dragPreview.style.position = 'absolute';
    dragPreview.style.top = '-1000px';
    document.body.appendChild(dragPreview);
    event.dataTransfer.setDragImage(dragPreview, 50, 25);
    
    setTimeout(() => {
      document.body.removeChild(dragPreview);
    }, 0);
  };

  const onDragEnd = (event) => {
    event.target.style.cursor = 'grab';
    setDraggedNode(null);
  };

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsOpen(true);
      } else if (window.innerWidth < 768) {
        setIsOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <>
      {/* Hamburger Button - Responsive */}
      <button
        className="sidebar-toggle"
        onClick={toggleSidebar}
        aria-label="Toggle node sidebar"
      >
        {isOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {/* Sidebar */}
      <div className={`node-sidebar ${isOpen ? 'open' : 'closed'}`}>
        {/* Header */}
        <div className="sidebar-header">
          <div className="sidebar-logo">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="none">
              <path d="M16 2L30 9v14L16 30 2 23V9L16 2z" fill="url(#gradient)" stroke="#8b5cf6" strokeWidth="1"/>
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#8b5cf6"/>
                  <stop offset="100%" stopColor="#6366f1"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
          <h2 className="sidebar-title">Nodes</h2>
          <button
            className="sidebar-close-btn"
            onClick={toggleSidebar}
            aria-label="Close sidebar"
          >
            <X size={16} />
          </button>
        </div>

        {/* Node List */}
        <div className="sidebar-content">
          {nodeTypes.map((node) => {
            const IconComponent = node.icon;
            return (
              <div
                key={node.type}
                className={`sidebar-node ${draggedNode === node.type ? 'dragging' : ''}`}
                draggable
                onDragStart={(event) => onDragStart(event, node.type)}
                onDragEnd={onDragEnd}
                style={{
                  '--node-color': node.color,
                  cursor: 'grab'
                }}
                title={node.description}
              >
                <div className="node-icon">
                  <IconComponent size={18} />
                </div>
                <div className="node-info">
                  <span className="node-label">{node.label}</span>
                  <span className="node-description">{node.description}</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="sidebar-overlay md:hidden"
          onClick={toggleSidebar}
        />
      )}
    </>
  );
};
