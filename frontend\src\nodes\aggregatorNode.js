// aggregatorNode.js
// Demonstrates data aggregation with multiple inputs and operations

import { useState } from 'react';
import { BaseNode, createHandle, commonLabelStyle } from './BaseNode';
import { Position } from 'reactflow';
import { NodeInput } from '../components/NodeInput';

export const AggregatorNode = ({ id, data }) => {
  const [operation, setOperation] = useState(data?.operation || 'concat');
  const [separator, setSeparator] = useState(data?.separator || ', ');

  const handleOperationChange = (e) => {
    setOperation(e.target.value);
  };

  const handleSeparatorChange = (e) => {
    setSeparator(e.target.value);
  };

  const handles = [
    createHandle(`${id}-input1`, 'target', Position.Left, { top: '20%' }),
    createHandle(`${id}-input2`, 'target', Position.Left, { top: '40%' }),
    createHandle(`${id}-input3`, 'target', Position.Left, { top: '60%' }),
    createHandle(`${id}-input4`, 'target', Position.Left, { top: '80%' }),
    createHandle(`${id}-result`, 'source', Position.Right)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Aggregator"
      handles={handles}
      nodeType="aggregator"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={commonLabelStyle}>
          Operation:
          <select
            value={operation}
            onChange={handleOperationChange}
            className="node-input"
          >
            <option value="concat">Concat</option>
            <option value="sum">Sum</option>
            <option value="avg">Average</option>
            <option value="max">Max</option>
            <option value="min">Min</option>
          </select>
        </label>
        {operation === 'concat' && (
          <label style={commonLabelStyle}>
            Separator:
            <NodeInput
              value={separator}
              onChange={handleSeparatorChange}
              placeholder="Enter separator"
              style={{ fontSize: '11px' }}
            />
          </label>
        )}
        <div style={{
          fontSize: '10px',
          color: 'rgba(255, 255, 255, 0.6)',
          fontStyle: 'italic',
          textAlign: 'center',
          padding: '4px 8px',
          backgroundColor: 'rgba(138, 43, 226, 0.1)',
          borderRadius: '4px',
          border: '1px solid rgba(138, 43, 226, 0.2)'
        }}>
          Combines multiple inputs
        </div>
      </div>
    </BaseNode>
  );
};
