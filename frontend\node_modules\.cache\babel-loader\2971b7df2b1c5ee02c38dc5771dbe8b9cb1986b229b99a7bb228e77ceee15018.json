{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\App.js\";\nimport { NodeSidebar } from './components/NodeSidebar';\nimport { PipelineUI } from './ui';\nimport { SubmitButton } from './submit';\nimport './components/NodeSidebar.css';\n\n// Debug logging for image/resource loading issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconsole.log('App.js loaded successfully');\n\n// Check if fonts are loading\ndocument.fonts.ready.then(() => {\n  console.log('Fonts loaded successfully');\n}).catch(error => {\n  console.error('Font loading error:', error);\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(NodeSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(PipelineUI, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SubmitButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["NodeSidebar", "PipelineUI", "SubmitButton", "jsxDEV", "_jsxDEV", "console", "log", "document", "fonts", "ready", "then", "catch", "error", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/App.js"], "sourcesContent": ["import { NodeSidebar } from './components/NodeSidebar';\nimport { PipelineUI } from './ui';\nimport { SubmitButton } from './submit';\nimport './components/NodeSidebar.css';\n\n// Debug logging for image/resource loading issues\nconsole.log('App.js loaded successfully');\n\n// Check if fonts are loading\ndocument.fonts.ready.then(() => {\n  console.log('Fonts loaded successfully');\n}).catch((error) => {\n  console.error('Font loading error:', error);\n});\n\nfunction App() {\n  return (\n    <div className=\"app-container\">\n      <NodeSidebar />\n      <div className=\"main-content\">\n        <PipelineUI />\n        <SubmitButton />\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAO,8BAA8B;;AAErC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;AAEzC;AACAC,QAAQ,CAACC,KAAK,CAACC,KAAK,CAACC,IAAI,CAAC,MAAM;EAC9BL,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;AAC1C,CAAC,CAAC,CAACK,KAAK,CAAEC,KAAK,IAAK;EAClBP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;AAC7C,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBACET,OAAA;IAAKU,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BX,OAAA,CAACJ,WAAW;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACff,OAAA;MAAKU,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BX,OAAA,CAACH,UAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACdf,OAAA,CAACF,YAAY;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GAVQP,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}