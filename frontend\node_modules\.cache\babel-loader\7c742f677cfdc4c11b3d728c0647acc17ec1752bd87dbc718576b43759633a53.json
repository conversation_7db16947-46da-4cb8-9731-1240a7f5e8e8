{"ast": null, "code": "/**\n * @license lucide-react v0.518.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5.42 9.42 8 12\",\n  key: \"12pkuq\"\n}], [\"circle\", {\n  cx: \"4\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"107mxr\"\n}], [\"path\", {\n  d: \"m14 6-8.58 8.58\",\n  key: \"gvzu5l\"\n}], [\"circle\", {\n  cx: \"4\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1ehqvc\"\n}], [\"path\", {\n  d: \"M10.8 14.8 14 18\",\n  key: \"ax7m9r\"\n}], [\"path\", {\n  d: \"M16 12h-2\",\n  key: \"10asgb\"\n}], [\"path\", {\n  d: \"M22 12h-2\",\n  key: \"14jgyd\"\n}]];\nconst ScissorsLineDashed = createLucideIcon(\"scissors-line-dashed\", __iconNode);\nexport { __iconNode, ScissorsLineDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "ScissorsLineDashed", "createLucideIcon"], "sources": ["D:\\Job Assesment\\frontend_technical_assessment\\node_modules\\lucide-react\\src\\icons\\scissors-line-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5.42 9.42 8 12', key: '12pkuq' }],\n  ['circle', { cx: '4', cy: '8', r: '2', key: '107mxr' }],\n  ['path', { d: 'm14 6-8.58 8.58', key: 'gvzu5l' }],\n  ['circle', { cx: '4', cy: '16', r: '2', key: '1ehqvc' }],\n  ['path', { d: 'M10.8 14.8 14 18', key: 'ax7m9r' }],\n  ['path', { d: 'M16 12h-2', key: '10asgb' }],\n  ['path', { d: 'M22 12h-2', key: '14jgyd' }],\n];\n\n/**\n * @component @name ScissorsLineDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNS40MiA5LjQyIDggMTIiIC8+CiAgPGNpcmNsZSBjeD0iNCIgY3k9IjgiIHI9IjIiIC8+CiAgPHBhdGggZD0ibTE0IDYtOC41OCA4LjU4IiAvPgogIDxjaXJjbGUgY3g9IjQiIGN5PSIxNiIgcj0iMiIgLz4KICA8cGF0aCBkPSJNMTAuOCAxNC44IDE0IDE4IiAvPgogIDxwYXRoIGQ9Ik0xNiAxMmgtMiIgLz4KICA8cGF0aCBkPSJNMjIgMTJoLTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/scissors-line-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ScissorsLineDashed = createLucideIcon('scissors-line-dashed', __iconNode);\n\nexport default ScissorsLineDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAI,kBAAA,GAAqBC,gBAAiB,yBAAwBP,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}