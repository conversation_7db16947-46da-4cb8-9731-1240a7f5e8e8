// ui.js
// Displays the drag-and-drop UI
// --------------------------------------------------

import { useState, useRef, useCallback } from 'react';
import ReactFlow, { Controls, Background, MiniMap } from 'reactflow';
import { useStore } from './store';
import { shallow } from 'zustand/shallow';
import { InputNode } from './nodes/inputNode';
import { LLMNode } from './nodes/llmNode';
import { OutputNode } from './nodes/outputNode';
import { TextNode } from './nodes/textNode';
import { MathNode } from './nodes/mathNode';
import { FilterNode } from './nodes/filterNode';
import { TimerNode } from './nodes/timerNode';
import { SwitchNode } from './nodes/switchNode';
import { AggregatorNode } from './nodes/aggregatorNode';

import 'reactflow/dist/style.css';

const gridSize = 20;
const proOptions = { hideAttribution: true };
const nodeTypes = {
  customInput: InputNode,
  llm: LLMNode,
  customOutput: OutputNode,
  text: TextNode,
  math: MathNode,
  filter: FilterNode,
  timer: TimerNode,
  switch: SwitchNode,
  aggregator: AggregatorNode,
};

const selector = (state) => ({
  nodes: state.nodes,
  edges: state.edges,
  getNodeID: state.getNodeID,
  addNode: state.addNode,
  onNodesChange: state.onNodesChange,
  onEdgesChange: state.onEdgesChange,
  onConnect: state.onConnect,
});

export const PipelineUI = () => {
    const reactFlowWrapper = useRef(null);
    const [reactFlowInstance, setReactFlowInstance] = useState(null);
    const [isDragOver, setIsDragOver] = useState(false);
    const {
      nodes,
      edges,
      getNodeID,
      addNode,
      onNodesChange,
      onEdgesChange,
      onConnect
    } = useStore(selector, shallow);

    const getInitNodeData = (nodeID, type) => {
      let nodeData = { id: nodeID, nodeType: `${type}` };
      return nodeData;
    }

    const onDrop = useCallback(
        (event) => {
          event.preventDefault();
          setIsDragOver(false);

          const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
          const dragData = event?.dataTransfer?.getData('application/reactflow');

          if (dragData) {
            const appData = JSON.parse(dragData);
            const type = appData?.nodeType;

            // check if the dropped element is valid
            if (typeof type === 'undefined' || !type) {
              return;
            }

            if (!reactFlowInstance) {
              return;
            }

            const position = reactFlowInstance.project({
              x: event.clientX - reactFlowBounds.left,
              y: event.clientY - reactFlowBounds.top,
            });

            const nodeID = getNodeID(type);
            const newNode = {
              id: nodeID,
              type,
              position,
              data: getInitNodeData(nodeID, type),
            };

            addNode(newNode);
          }
        },
        [reactFlowInstance, addNode, getNodeID]
    );

    const onDragOver = useCallback((event) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
        setIsDragOver(true);
    }, []);

    const onDragLeave = useCallback((event) => {
        // Only set to false if we're leaving the main container
        if (!event.currentTarget.contains(event.relatedTarget)) {
          setIsDragOver(false);
        }
    }, []);

    return (
        <div className={`pipeline-ui-container ${isDragOver ? 'drag-over' : ''}`}>
            {isDragOver && (
                <div className="drop-indicator">
                    <div className="drop-indicator-content">
                        <div className="drop-icon">📦</div>
                        <div className="drop-text">Drop node here</div>
                    </div>
                </div>
            )}
            <div
                ref={reactFlowWrapper}
                style={{
                    width: '100%',
                    height: '100%',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0
                }}
            >
                <ReactFlow
                    nodes={nodes}
                    edges={edges}
                    onNodesChange={onNodesChange}
                    onEdgesChange={onEdgesChange}
                    onConnect={onConnect}
                    onDrop={onDrop}
                    onDragOver={onDragOver}
                    onDragLeave={onDragLeave}
                    onInit={setReactFlowInstance}
                    nodeTypes={nodeTypes}
                    proOptions={proOptions}
                    snapGrid={[gridSize, gridSize]}
                    connectionLineType='smoothstep'
                    style={{
                        background: 'transparent',
                        width: '100%',
                        height: '100%'
                    }}
                >
                    <Background
                        variant="dots"
                        gap={25}
                        size={2}
                        color="#a855f7"
                        style={{ opacity: 0.4 }}
                    />
                    <Controls
                        style={{
                            button: {
                                backgroundColor: 'rgba(26, 11, 46, 0.9)',
                                color: '#ffffff',
                                border: '1px solid rgba(138, 43, 226, 0.4)',
                            }
                        }}
                    />
                    <MiniMap
                        nodeColor={(node) => {
                            const colors = {
                                customInput: 'rgba(140, 79, 255, 0.6)',
                                llm: 'rgba(183, 112, 255, 0.6)',
                                customOutput: 'rgba(140, 79, 255, 0.6)',
                                text: 'rgba(16, 185, 129, 0.6)',
                                math: 'rgba(59, 130, 246, 0.6)',
                                filter: 'rgba(245, 158, 11, 0.6)',
                                timer: 'rgba(239, 68, 68, 0.6)',
                                switch: 'rgba(6, 182, 212, 0.6)',
                                aggregator: 'rgba(236, 72, 153, 0.6)'
                            };
                            return colors[node.type] || 'rgba(140, 79, 255, 0.4)';
                        }}
                        maskColor="rgba(15, 12, 26, 0.7)"
                        maskStrokeColor="rgba(168, 85, 247, 0.8)"
                        maskStrokeWidth={2}
                        nodeStrokeWidth={1}
                        nodeBorderRadius={3}
                        pannable={true}
                        zoomable={true}
                        offsetScale={3}
                    />

                </ReactFlow>
            </div>
        </div>
    )
}
