{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\mathNode.js\",\n  _s = $RefreshSig$();\n// mathNode.js\n// Demonstrates mathematical operations with multiple inputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const MathNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [operation, setOperation] = useState((data === null || data === void 0 ? void 0 : data.operation) || 'add');\n  const handleOperationChange = e => {\n    setOperation(e.target.value);\n  };\n  const handles = [createHandle(`${id}-input1`, 'target', Position.Left, {\n    top: '25%'\n  }), createHandle(`${id}-input2`, 'target', Position.Left, {\n    top: '75%'\n  }), createHandle(`${id}-result`, 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Math\",\n    handles: handles,\n    nodeType: \"math\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Operation:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: operation,\n          onChange: handleOperationChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"add\",\n            children: \"Add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"subtract\",\n            children: \"Subtract\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"multiply\",\n            children: \"Multiply\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"divide\",\n            children: \"Divide\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)'\n        },\n        children: [\"Performs \", operation, \" operation\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(MathNode, \"R7FWnHhCWCtLxw0vmLO8tIvK/QI=\");\n_c = MathNode;\nvar _c;\n$RefreshReg$(_c, \"MathNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "commonLabelStyle", "Position", "jsxDEV", "_jsxDEV", "MathNode", "id", "data", "_s", "operation", "setOperation", "handleOperationChange", "e", "target", "value", "handles", "Left", "top", "Right", "title", "nodeType", "children", "onChange", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/mathNode.js"], "sourcesContent": ["// mathNode.js\n// Demonstrates mathematical operations with multiple inputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\n\nexport const MathNode = ({ id, data }) => {\n  const [operation, setOperation] = useState(data?.operation || 'add');\n\n  const handleOperationChange = (e) => {\n    setOperation(e.target.value);\n  };\n\n  const handles = [\n    createHandle(`${id}-input1`, 'target', Position.Left, { top: '25%' }),\n    createHandle(`${id}-input2`, 'target', Position.Left, { top: '75%' }),\n    createHandle(`${id}-result`, 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Math\"\n      handles={handles}\n      nodeType=\"math\"\n    >\n      <div>\n        <label>\n          Operation:\n          <select\n            value={operation}\n            onChange={handleOperationChange}\n            className=\"node-input\"\n          >\n            <option value=\"add\">Add</option>\n            <option value=\"subtract\">Subtract</option>\n            <option value=\"multiply\">Multiply</option>\n            <option value=\"divide\">Divide</option>\n          </select>\n        </label>\n        <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>\n          Performs {operation} operation\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AACrE,SAASC,QAAQ,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,KAAK,CAAC;EAEpE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,OAAO,GAAG,CACdf,YAAY,CAAE,GAAEM,EAAG,SAAQ,EAAE,QAAQ,EAAEJ,QAAQ,CAACc,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrEjB,YAAY,CAAE,GAAEM,EAAG,SAAQ,EAAE,QAAQ,EAAEJ,QAAQ,CAACc,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrEjB,YAAY,CAAE,GAAEM,EAAG,SAAQ,EAAE,QAAQ,EAAEJ,QAAQ,CAACgB,KAAK,CAAC,CACvD;EAED,oBACEd,OAAA,CAACL,QAAQ;IACPO,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXY,KAAK,EAAC,MAAM;IACZJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,MAAM;IAAAC,QAAA,eAEfjB,OAAA;MAAAiB,QAAA,gBACEjB,OAAA;QAAAiB,QAAA,GAAO,YAEL,eAAAjB,OAAA;UACEU,KAAK,EAAEL,SAAU;UACjBa,QAAQ,EAAEX,qBAAsB;UAChCY,SAAS,EAAC,YAAY;UAAAF,QAAA,gBAEtBjB,OAAA;YAAQU,KAAK,EAAC,KAAK;YAAAO,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCvB,OAAA;YAAQU,KAAK,EAAC,UAAU;YAAAO,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CvB,OAAA;YAAQU,KAAK,EAAC,UAAU;YAAAO,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CvB,OAAA;YAAQU,KAAK,EAAC,QAAQ;YAAAO,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRvB,OAAA;QAAKwB,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAA2B,CAAE;QAAAT,QAAA,GAAC,WAC1D,EAACZ,SAAS,EAAC,YACtB;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACnB,EAAA,CAzCWH,QAAQ;AAAA0B,EAAA,GAAR1B,QAAQ;AAAA,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}