{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\filterNode.js\",\n  _s = $RefreshSig$();\n// filterNode.js\n// Demonstrates data filtering with configurable conditions\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const FilterNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [condition, setCondition] = useState((data === null || data === void 0 ? void 0 : data.condition) || 'contains');\n  const [value, setValue] = useState((data === null || data === void 0 ? void 0 : data.value) || '');\n  const handleConditionChange = e => {\n    setCondition(e.target.value);\n  };\n  const handleValueChange = e => {\n    setValue(e.target.value);\n  };\n  const handles = [createHandle(`${id}-input`, 'target', Position.Left), createHandle(`${id}-filtered`, 'source', Position.Right, {\n    top: '30%'\n  }), createHandle(`${id}-rejected`, 'source', Position.Right, {\n    top: '70%'\n  })];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Filter\",\n    handles: handles,\n    nodeType: \"filter\",\n    height: 100,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Condition:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: condition,\n          onChange: handleConditionChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"contains\",\n            children: \"Contains\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"equals\",\n            children: \"Equals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"startsWith\",\n            children: \"Starts with\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Value:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: value,\n          onChange: handleValueChange,\n          className: \"node-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(FilterNode, \"w8eFE1CGItfZ9+5TtbJeH3kfyts=\");\n_c = FilterNode;\nvar _c;\n$RefreshReg$(_c, \"FilterNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "commonLabelStyle", "Position", "jsxDEV", "_jsxDEV", "FilterNode", "id", "data", "_s", "condition", "setCondition", "value", "setValue", "handleConditionChange", "e", "target", "handleValueChange", "handles", "Left", "Right", "top", "title", "nodeType", "height", "children", "onChange", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/filterNode.js"], "sourcesContent": ["// filterNode.js\n// Demonstrates data filtering with configurable conditions\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\n\nexport const FilterNode = ({ id, data }) => {\n  const [condition, setCondition] = useState(data?.condition || 'contains');\n  const [value, setValue] = useState(data?.value || '');\n\n  const handleConditionChange = (e) => {\n    setCondition(e.target.value);\n  };\n\n  const handleValueChange = (e) => {\n    setValue(e.target.value);\n  };\n\n  const handles = [\n    createHandle(`${id}-input`, 'target', Position.Left),\n    createHandle(`${id}-filtered`, 'source', Position.Right, { top: '30%' }),\n    createHandle(`${id}-rejected`, 'source', Position.Right, { top: '70%' })\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Filter\"\n      handles={handles}\n      nodeType=\"filter\"\n      height={100}\n    >\n      <div>\n        <label>\n          Condition:\n          <select\n            value={condition}\n            onChange={handleConditionChange}\n            className=\"node-input\"\n          >\n            <option value=\"contains\">Contains</option>\n            <option value=\"equals\">Equals</option>\n            <option value=\"startsWith\">Starts with</option>\n          </select>\n        </label>\n        <label>\n          Value:\n          <input\n            type=\"text\"\n            value={value}\n            onChange={handleValueChange}\n            className=\"node-input\"\n          />\n        </label>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AACrE,SAASC,QAAQ,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,UAAU,CAAC;EACzE,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,KAAK,KAAI,EAAE,CAAC;EAErD,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMK,iBAAiB,GAAIF,CAAC,IAAK;IAC/BF,QAAQ,CAACE,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMM,OAAO,GAAG,CACdjB,YAAY,CAAE,GAAEM,EAAG,QAAO,EAAE,QAAQ,EAAEJ,QAAQ,CAACgB,IAAI,CAAC,EACpDlB,YAAY,CAAE,GAAEM,EAAG,WAAU,EAAE,QAAQ,EAAEJ,QAAQ,CAACiB,KAAK,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACxEpB,YAAY,CAAE,GAAEM,EAAG,WAAU,EAAE,QAAQ,EAAEJ,QAAQ,CAACiB,KAAK,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,CACzE;EAED,oBACEhB,OAAA,CAACL,QAAQ;IACPO,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXc,KAAK,EAAC,QAAQ;IACdJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,QAAQ;IACjBC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAEZpB,OAAA;MAAAoB,QAAA,gBACEpB,OAAA;QAAAoB,QAAA,GAAO,YAEL,eAAApB,OAAA;UACEO,KAAK,EAAEF,SAAU;UACjBgB,QAAQ,EAAEZ,qBAAsB;UAChCa,SAAS,EAAC,YAAY;UAAAF,QAAA,gBAEtBpB,OAAA;YAAQO,KAAK,EAAC,UAAU;YAAAa,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C1B,OAAA;YAAQO,KAAK,EAAC,QAAQ;YAAAa,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC1B,OAAA;YAAQO,KAAK,EAAC,YAAY;YAAAa,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACR1B,OAAA;QAAAoB,QAAA,GAAO,QAEL,eAAApB,OAAA;UACE2B,IAAI,EAAC,MAAM;UACXpB,KAAK,EAAEA,KAAM;UACbc,QAAQ,EAAET,iBAAkB;UAC5BU,SAAS,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACtB,EAAA,CApDWH,UAAU;AAAA2B,EAAA,GAAV3B,UAAU;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}