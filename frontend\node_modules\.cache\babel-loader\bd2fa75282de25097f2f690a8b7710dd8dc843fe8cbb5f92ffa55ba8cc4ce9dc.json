{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\aggregatorNode.js\",\n  _s = $RefreshSig$();\n// aggregatorNode.js\n// Demonstrates data aggregation with multiple inputs and operations\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { NodeInput } from '../components/NodeInput';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AggregatorNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [operation, setOperation] = useState((data === null || data === void 0 ? void 0 : data.operation) || 'concat');\n  const [separator, setSeparator] = useState((data === null || data === void 0 ? void 0 : data.separator) || ', ');\n  const handleOperationChange = e => {\n    setOperation(e.target.value);\n  };\n  const handleSeparatorChange = e => {\n    setSeparator(e.target.value);\n  };\n  const handles = [createHandle(`${id}-input1`, 'target', Position.Left, {\n    top: '20%'\n  }), createHandle(`${id}-input2`, 'target', Position.Left, {\n    top: '40%'\n  }), createHandle(`${id}-input3`, 'target', Position.Left, {\n    top: '60%'\n  }), createHandle(`${id}-input4`, 'target', Position.Left, {\n    top: '80%'\n  }), createHandle(`${id}-result`, 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Aggregator\",\n    handles: handles,\n    nodeType: \"aggregator\",\n    height: 120,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Operation:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: operation,\n          onChange: handleOperationChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"concat\",\n            children: \"Concat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"sum\",\n            children: \"Sum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"avg\",\n            children: \"Average\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"max\",\n            children: \"Max\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"min\",\n            children: \"Min\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), operation === 'concat' && /*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Separator:\", /*#__PURE__*/_jsxDEV(NodeInput, {\n          value: separator,\n          onChange: handleSeparatorChange,\n          placeholder: \"Enter separator\",\n          style: {\n            fontSize: '10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '9px',\n          color: 'rgba(255, 255, 255, 0.6)'\n        },\n        children: \"Combines multiple inputs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(AggregatorNode, \"sMY8CXrz1Xg3U6xCkKPfMgyFeGs=\");\n_c = AggregatorNode;\nvar _c;\n$RefreshReg$(_c, \"AggregatorNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "commonLabelStyle", "Position", "NodeInput", "jsxDEV", "_jsxDEV", "AggregatorNode", "id", "data", "_s", "operation", "setOperation", "separator", "setSeparator", "handleOperationChange", "e", "target", "value", "handleSeparatorChange", "handles", "Left", "top", "Right", "title", "nodeType", "height", "children", "onChange", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "style", "fontSize", "color", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/aggregatorNode.js"], "sourcesContent": ["// aggregatorNode.js\n// Demonstrates data aggregation with multiple inputs and operations\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { NodeInput } from '../components/NodeInput';\n\nexport const AggregatorNode = ({ id, data }) => {\n  const [operation, setOperation] = useState(data?.operation || 'concat');\n  const [separator, setSeparator] = useState(data?.separator || ', ');\n\n  const handleOperationChange = (e) => {\n    setOperation(e.target.value);\n  };\n\n  const handleSeparatorChange = (e) => {\n    setSeparator(e.target.value);\n  };\n\n  const handles = [\n    createHandle(`${id}-input1`, 'target', Position.Left, { top: '20%' }),\n    createHandle(`${id}-input2`, 'target', Position.Left, { top: '40%' }),\n    createHandle(`${id}-input3`, 'target', Position.Left, { top: '60%' }),\n    createHandle(`${id}-input4`, 'target', Position.Left, { top: '80%' }),\n    createHandle(`${id}-result`, 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Aggregator\"\n      handles={handles}\n      nodeType=\"aggregator\"\n      height={120}\n    >\n      <div>\n        <label>\n          Operation:\n          <select\n            value={operation}\n            onChange={handleOperationChange}\n            className=\"node-input\"\n          >\n            <option value=\"concat\">Concat</option>\n            <option value=\"sum\">Sum</option>\n            <option value=\"avg\">Average</option>\n            <option value=\"max\">Max</option>\n            <option value=\"min\">Min</option>\n          </select>\n        </label>\n        {operation === 'concat' && (\n          <label>\n            Separator:\n            <NodeInput\n              value={separator}\n              onChange={handleSeparatorChange}\n              placeholder=\"Enter separator\"\n              style={{ fontSize: '10px' }}\n            />\n          </label>\n        )}\n        <div style={{ fontSize: '9px', color: 'rgba(255, 255, 255, 0.6)' }}>\n          Combines multiple inputs\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AACrE,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,SAAS,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,QAAQ,CAAC;EACvE,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,KAAI,IAAI,CAAC;EAEnE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,qBAAqB,GAAIH,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAME,OAAO,GAAG,CACdnB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACkB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrErB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACkB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrErB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACkB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrErB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACkB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrErB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACoB,KAAK,CAAC,CACvD;EAED,oBACEjB,OAAA,CAACN,QAAQ;IACPQ,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAC,YAAY;IAClBJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,YAAY;IACrBC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAEZrB,OAAA;MAAAqB,QAAA,gBACErB,OAAA;QAAAqB,QAAA,GAAO,YAEL,eAAArB,OAAA;UACEY,KAAK,EAAEP,SAAU;UACjBiB,QAAQ,EAAEb,qBAAsB;UAChCc,SAAS,EAAC,YAAY;UAAAF,QAAA,gBAEtBrB,OAAA;YAAQY,KAAK,EAAC,QAAQ;YAAAS,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC3B,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAS,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC3B,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAS,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC3B,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAS,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC3B,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAS,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACPtB,SAAS,KAAK,QAAQ,iBACrBL,OAAA;QAAAqB,QAAA,GAAO,YAEL,eAAArB,OAAA,CAACF,SAAS;UACRc,KAAK,EAAEL,SAAU;UACjBe,QAAQ,EAAET,qBAAsB;UAChCe,WAAW,EAAC,iBAAiB;UAC7BC,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAO;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR,eACD3B,OAAA;QAAK6B,KAAK,EAAE;UAAEC,QAAQ,EAAE,KAAK;UAAEC,KAAK,EAAE;QAA2B,CAAE;QAAAV,QAAA,EAAC;MAEpE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACvB,EAAA,CA7DWH,cAAc;AAAA+B,EAAA,GAAd/B,cAAc;AAAA,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}