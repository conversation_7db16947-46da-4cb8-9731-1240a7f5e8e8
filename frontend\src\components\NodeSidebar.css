/* NodeSidebar.css */
/* Vertical left sidebar for draggable nodes with responsive design */

/* Sidebar Toggle But<PERSON> (Mobile/Tablet) */
.sidebar-toggle {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1001;
  background: linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
  border: 1px solid rgba(138, 43, 226, 0.6);
  border-radius: 8px;
  padding: 8px;
  color: #ffffff;
  cursor: pointer;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 16px rgba(138, 43, 226, 0.3);
}

.sidebar-toggle:hover {
  background: linear-gradient(135deg, rgba(26, 11, 46, 0.98) 0%, rgba(22, 33, 62, 0.98) 100%);
  border-color: rgba(138, 43, 226, 0.8);
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(138, 43, 226, 0.4);
}

/* Main Sidebar */
.node-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  min-width: 280px;
  max-width: 320px;
  background: linear-gradient(180deg, #0e0e23 0%, #1a0b2e 100%);
  border-right: 1px solid rgba(138, 43, 226, 0.3);
  box-shadow: 
    inset -1px 0 0 rgba(138, 43, 226, 0.2),
    4px 0 20px rgba(138, 43, 226, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
}

.node-sidebar.closed {
  transform: translateX(-100%);
}

.node-sidebar.open {
  transform: translateX(0);
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  border-bottom: 1px solid rgba(138, 43, 226, 0.2);
  background: linear-gradient(135deg, rgba(26, 11, 46, 0.8) 0%, rgba(22, 33, 62, 0.8) 100%);
}

.sidebar-logo svg {
  filter: drop-shadow(0 0 8px rgba(138, 43, 226, 0.5));
}

.sidebar-title {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  flex: 1;
  background: linear-gradient(90deg, #ffffff 0%, #e2e8f0 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sidebar-close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 200ms ease;
}

.sidebar-close-btn:hover {
  background: rgba(138, 43, 226, 0.2);
  color: #ffffff;
}

/* Sidebar Content */
.sidebar-content {
  flex: 1;
  padding: 16px 12px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(138, 43, 226, 0.5) transparent;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
}

/* Sidebar Node Items */
.sidebar-node {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(26, 11, 46, 0.6) 0%, rgba(22, 33, 62, 0.6) 100%);
  border: 1px solid rgba(138, 43, 226, 0.3);
  cursor: grab;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.sidebar-node::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--node-color, #8b5cf6);
  opacity: 0.8;
  transition: opacity 300ms ease;
}

.sidebar-node:hover {
  background: linear-gradient(135deg, rgba(26, 11, 46, 0.8) 0%, rgba(22, 33, 62, 0.8) 100%);
  border-color: rgba(138, 43, 226, 0.6);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 8px 25px rgba(138, 43, 226, 0.3),
    0 0 0 1px rgba(138, 43, 226, 0.2);
}

.sidebar-node:hover::before {
  opacity: 1;
}

.sidebar-node:active,
.sidebar-node.dragging {
  cursor: grabbing;
  transform: scale(0.98);
  opacity: 0.8;
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: linear-gradient(135deg, var(--node-color, #8b5cf6) 0%, rgba(255, 255, 255, 0.1) 100%);
  color: #ffffff;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.node-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.node-label {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.2;
}

.node-description {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Sidebar Overlay (Mobile) */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .node-sidebar {
    min-width: 260px;
    max-width: 280px;
  }
  
  .sidebar-header {
    padding: 16px 12px;
  }
  
  .sidebar-content {
    padding: 12px 8px;
  }
  
  .sidebar-node {
    padding: 10px 12px;
  }
  
  .node-icon {
    width: 32px;
    height: 32px;
  }
  
  .node-label {
    font-size: 13px;
  }
  
  .node-description {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .node-sidebar {
    min-width: 240px;
    max-width: 260px;
  }
}

/* Tablet: Show toggle button */
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar-toggle {
    display: block;
  }
}

/* Desktop: Show toggle button but sidebar open by default */
@media (min-width: 1024px) {
  .sidebar-toggle {
    display: block;
    top: 24px;
    left: 24px;
  }

  .node-sidebar.closed {
    transform: translateX(-240px);
  }

  .node-sidebar.closed .sidebar-header,
  .node-sidebar.closed .sidebar-content {
    opacity: 0;
    transition: opacity 200ms ease;
  }

  .node-sidebar.open .sidebar-header,
  .node-sidebar.open .sidebar-content {
    opacity: 1;
    transition: opacity 300ms ease 100ms;
  }
}

/* Animation for drag preview */
@keyframes dragPreview {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(0.9) rotate(2deg);
    opacity: 0.7;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow:
      0 8px 25px rgba(138, 43, 226, 0.3),
      0 0 0 1px rgba(138, 43, 226, 0.2);
  }
  50% {
    box-shadow:
      0 8px 25px rgba(138, 43, 226, 0.5),
      0 0 0 1px rgba(138, 43, 226, 0.4);
  }
}

.sidebar-node.dragging {
  animation: dragPreview 200ms ease-out, pulseGlow 1s ease-in-out infinite;
  z-index: 1000;
}

/* Add a subtle glow to the entire sidebar when dragging */
.node-sidebar:has(.sidebar-node.dragging) {
  box-shadow:
    inset -1px 0 0 rgba(138, 43, 226, 0.3),
    4px 0 20px rgba(138, 43, 226, 0.2);
}
