{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\BaseNode.js\";\n// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 300,\n  // Increased default width\n  height = 120,\n  // Increased default height\n  nodeType = 'default'\n}) => {\n  // Improved sizing with min/max constraints\n  const nodeWidth = Math.max(280, Math.min(320, width));\n  const nodeHeight = Math.max(100, Math.min(140, height));\n  const defaultStyle = {\n    width: nodeWidth,\n    minWidth: 280,\n    maxWidth: 320,\n    minHeight: nodeHeight,\n    background: 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)',\n    border: '1px solid rgba(138, 43, 226, 0.6)',\n    borderRadius: '12px',\n    // Reduced from 16px to 12px as requested\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    alignItems: 'stretch',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n    position: 'relative',\n    color: '#ffffff',\n    backdropFilter: 'blur(20px)',\n    boxShadow: '0 8px 32px rgba(138, 43, 226, 0.3)',\n    boxSizing: 'border-box',\n    ...style\n  };\n  const titleStyle = {\n    fontWeight: '600',\n    fontSize: '12px',\n    color: '#ffffff',\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: '1px solid rgba(138, 43, 226, 0.3)',\n    paddingBottom: theme.spacing.xs,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent'\n  };\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    fontSize: theme.typography.fontSize.xs,\n    gap: '8px',\n    // Consistent 8px gap for all content\n    paddingTop: theme.spacing.xs\n  };\n  const handleStyle = {\n    width: '12px',\n    height: '12px',\n    background: 'linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)',\n    border: '2px solid #ffffff',\n    borderRadius: '50%',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    boxShadow: '0 2px 8px rgba(138, 43, 226, 0.4)',\n    zIndex: 10\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: defaultStyle,\n    className: `base-node ${className}`,\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';\n      e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.8)';\n      e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)';\n      e.currentTarget.style.boxShadow = '0 12px 40px rgba(138, 43, 226, 0.3)';\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0) scale(1)';\n      e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.4)';\n      e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.9) 0%, rgba(22, 33, 62, 0.9) 100%)';\n      e.currentTarget.style.boxShadow = '0 8px 32px rgba(138, 43, 226, 0.2)';\n    },\n    children: [handles.map((handle, index) => /*#__PURE__*/_jsxDEV(Handle, {\n      type: handle.type,\n      position: handle.position,\n      id: `${id}-${handle.id}`,\n      style: {\n        ...handleStyle,\n        ...handle.style\n      }\n    }, `${id}-${handle.id || index}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this)), title && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: titleStyle,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: contentStyle,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to create handle configurations\n_c = BaseNode;\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (handleId = 'output') => createHandle(handleId, 'source', Position.Right),\n  targetLeft: (handleId = 'input') => createHandle(handleId, 'target', Position.Left),\n  targetTop: (handleId = 'input') => createHandle(handleId, 'target', Position.Top),\n  sourceBottom: (handleId = 'output') => createHandle(handleId, 'source', Position.Bottom)\n};\n\n// Common label styling for form inputs - extracted to maintain consistency\nexport const commonLabelStyle = {\n  fontSize: '12px',\n  display: 'flex',\n  flexDirection: 'column',\n  gap: '8px',\n  // Increased from 4px to 8px for better spacing\n  color: '#ffffff',\n  fontWeight: '600',\n  marginBottom: '10px',\n  // Added consistent margin between label-input groups\n  width: '100%',\n  boxSizing: 'border-box'\n};\n\n// Inline label style for Timer node and similar layouts\nexport const inlineLabelStyle = {\n  fontSize: '12px',\n  display: 'flex',\n  flexDirection: 'row',\n  alignItems: 'center',\n  gap: '8px',\n  color: '#ffffff',\n  fontWeight: '600',\n  marginBottom: '10px',\n  width: '100%',\n  boxSizing: 'border-box'\n};\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Position", "theme", "jsxDEV", "_jsxDEV", "BaseNode", "id", "data", "title", "children", "handles", "style", "className", "width", "height", "nodeType", "nodeWidth", "Math", "max", "min", "nodeHeight", "defaultStyle", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "background", "border", "borderRadius", "padding", "spacing", "md", "display", "flexDirection", "justifyContent", "alignItems", "transition", "fontFamily", "position", "color", "<PERSON><PERSON>ilter", "boxShadow", "boxSizing", "titleStyle", "fontWeight", "fontSize", "marginBottom", "xs", "textAlign", "borderBottom", "paddingBottom", "textTransform", "letterSpacing", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "contentStyle", "flex", "typography", "gap", "paddingTop", "handleStyle", "zIndex", "onMouseEnter", "e", "currentTarget", "transform", "borderColor", "onMouseLeave", "map", "handle", "index", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "createHandle", "HANDLE_CONFIGS", "sourceRight", "handleId", "Right", "targetLeft", "Left", "targetTop", "Top", "sourceBottom", "Bottom", "commonLabelStyle", "inlineLabelStyle", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/BaseNode.js"], "sourcesContent": ["// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\n\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 300, // Increased default width\n  height = 120, // Increased default height\n  nodeType = 'default'\n}) => {\n\n  // Improved sizing with min/max constraints\n  const nodeWidth = Math.max(280, Math.min(320, width));\n  const nodeHeight = Math.max(100, Math.min(140, height));\n\n  const defaultStyle = {\n    width: nodeWidth,\n    minWidth: 280,\n    maxWidth: 320,\n    minHeight: nodeHeight,\n    background: 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)',\n    border: '1px solid rgba(138, 43, 226, 0.6)',\n    borderRadius: '12px', // Reduced from 16px to 12px as requested\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    alignItems: 'stretch',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n    position: 'relative',\n    color: '#ffffff',\n    backdropFilter: 'blur(20px)',\n    boxShadow: '0 8px 32px rgba(138, 43, 226, 0.3)',\n    boxSizing: 'border-box',\n    ...style\n  };\n\n  const titleStyle = {\n    fontWeight: '600',\n    fontSize: '12px',\n    color: '#ffffff',\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: '1px solid rgba(138, 43, 226, 0.3)',\n    paddingBottom: theme.spacing.xs,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n  };\n\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    fontSize: theme.typography.fontSize.xs,\n    gap: '8px', // Consistent 8px gap for all content\n    paddingTop: theme.spacing.xs,\n  };\n\n  const handleStyle = {\n    width: '12px',\n    height: '12px',\n    background: 'linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)',\n    border: '2px solid #ffffff',\n    borderRadius: '50%',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    boxShadow: '0 2px 8px rgba(138, 43, 226, 0.4)',\n    zIndex: 10,\n  };\n\n  return (\n    <div\n      style={defaultStyle}\n      className={`base-node ${className}`}\n      onMouseEnter={(e) => {\n        e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';\n        e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.8)';\n        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)';\n        e.currentTarget.style.boxShadow = '0 12px 40px rgba(138, 43, 226, 0.3)';\n      }}\n      onMouseLeave={(e) => {\n        e.currentTarget.style.transform = 'translateY(0) scale(1)';\n        e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.4)';\n        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.9) 0%, rgba(22, 33, 62, 0.9) 100%)';\n        e.currentTarget.style.boxShadow = '0 8px 32px rgba(138, 43, 226, 0.2)';\n      }}\n    >\n      {/* Render handles */}\n      {handles.map((handle, index) => (\n        <Handle\n          key={`${id}-${handle.id || index}`}\n          type={handle.type}\n          position={handle.position}\n          id={`${id}-${handle.id}`}\n          style={{\n            ...handleStyle,\n            ...handle.style\n          }}\n        />\n      ))}\n\n      {/* Title */}\n      {title && (\n        <div style={titleStyle}>\n          <span>{title}</span>\n        </div>\n      )}\n\n      {/* Content */}\n      <div style={contentStyle}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Helper function to create handle configurations\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (handleId = 'output') => createHandle(handleId, 'source', Position.Right),\n  targetLeft: (handleId = 'input') => createHandle(handleId, 'target', Position.Left),\n  targetTop: (handleId = 'input') => createHandle(handleId, 'target', Position.Top),\n  sourceBottom: (handleId = 'output') => createHandle(handleId, 'source', Position.Bottom),\n};\n\n// Common label styling for form inputs - extracted to maintain consistency\nexport const commonLabelStyle = {\n  fontSize: '12px',\n  display: 'flex',\n  flexDirection: 'column',\n  gap: '8px', // Increased from 4px to 8px for better spacing\n  color: '#ffffff',\n  fontWeight: '600',\n  marginBottom: '10px', // Added consistent margin between label-input groups\n  width: '100%',\n  boxSizing: 'border-box'\n};\n\n// Inline label style for Timer node and similar layouts\nexport const inlineLabelStyle = {\n  fontSize: '12px',\n  display: 'flex',\n  flexDirection: 'row',\n  alignItems: 'center',\n  gap: '8px',\n  color: '#ffffff',\n  fontWeight: '600',\n  marginBottom: '10px',\n  width: '100%',\n  boxSizing: 'border-box'\n};\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,WAAW;AAC5C,SAASC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EACvBC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACRC,OAAO,GAAG,EAAE;EACZC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,GAAG;EAAE;EACbC,MAAM,GAAG,GAAG;EAAE;EACdC,QAAQ,GAAG;AACb,CAAC,KAAK;EAEJ;EACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEN,KAAK,CAAC,CAAC;EACrD,MAAMO,UAAU,GAAGH,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEL,MAAM,CAAC,CAAC;EAEvD,MAAMO,YAAY,GAAG;IACnBR,KAAK,EAAEG,SAAS;IAChBM,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAEJ,UAAU;IACrBK,UAAU,EAAE,iFAAiF;IAC7FC,MAAM,EAAE,mCAAmC;IAC3CC,YAAY,EAAE,MAAM;IAAE;IACtBC,OAAO,EAAE1B,KAAK,CAAC2B,OAAO,CAACC,EAAE;IACzBC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,wCAAwC;IACpDC,UAAU,EAAE,2CAA2C;IACvDC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,SAAS;IAChBC,cAAc,EAAE,YAAY;IAC5BC,SAAS,EAAE,oCAAoC;IAC/CC,SAAS,EAAE,YAAY;IACvB,GAAG9B;EACL,CAAC;EAED,MAAM+B,UAAU,GAAG;IACjBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,MAAM;IAChBN,KAAK,EAAE,SAAS;IAChBO,YAAY,EAAE3C,KAAK,CAAC2B,OAAO,CAACiB,EAAE;IAC9BC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE,mCAAmC;IACjDC,aAAa,EAAE/C,KAAK,CAAC2B,OAAO,CAACiB,EAAE;IAC/BI,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,OAAO;IACtB1B,UAAU,EAAE,0CAA0C;IACtD2B,cAAc,EAAE,MAAM;IACtBC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE;EACvB,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE,CAAC;IACPzB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,YAAY;IAC5BW,QAAQ,EAAE1C,KAAK,CAACuD,UAAU,CAACb,QAAQ,CAACE,EAAE;IACtCY,GAAG,EAAE,KAAK;IAAE;IACZC,UAAU,EAAEzD,KAAK,CAAC2B,OAAO,CAACiB;EAC5B,CAAC;EAED,MAAMc,WAAW,GAAG;IAClB/C,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdW,UAAU,EAAE,mDAAmD;IAC/DC,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,KAAK;IACnBQ,UAAU,EAAE,wCAAwC;IACpDK,SAAS,EAAE,mCAAmC;IAC9CqB,MAAM,EAAE;EACV,CAAC;EAED,oBACEzD,OAAA;IACEO,KAAK,EAAEU,YAAa;IACpBT,SAAS,EAAG,aAAYA,SAAU,EAAE;IACpCkD,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAACrD,KAAK,CAACsD,SAAS,GAAG,8BAA8B;MAChEF,CAAC,CAACC,aAAa,CAACrD,KAAK,CAACuD,WAAW,GAAG,yBAAyB;MAC7DH,CAAC,CAACC,aAAa,CAACrD,KAAK,CAACc,UAAU,GAAG,iFAAiF;MACpHsC,CAAC,CAACC,aAAa,CAACrD,KAAK,CAAC6B,SAAS,GAAG,qCAAqC;IACzE,CAAE;IACF2B,YAAY,EAAGJ,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAACrD,KAAK,CAACsD,SAAS,GAAG,wBAAwB;MAC1DF,CAAC,CAACC,aAAa,CAACrD,KAAK,CAACuD,WAAW,GAAG,yBAAyB;MAC7DH,CAAC,CAACC,aAAa,CAACrD,KAAK,CAACc,UAAU,GAAG,+EAA+E;MAClHsC,CAAC,CAACC,aAAa,CAACrD,KAAK,CAAC6B,SAAS,GAAG,oCAAoC;IACxE,CAAE;IAAA/B,QAAA,GAGDC,OAAO,CAAC0D,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBlE,OAAA,CAACJ,MAAM;MAELuE,IAAI,EAAEF,MAAM,CAACE,IAAK;MAClBlC,QAAQ,EAAEgC,MAAM,CAAChC,QAAS;MAC1B/B,EAAE,EAAG,GAAEA,EAAG,IAAG+D,MAAM,CAAC/D,EAAG,EAAE;MACzBK,KAAK,EAAE;QACL,GAAGiD,WAAW;QACd,GAAGS,MAAM,CAAC1D;MACZ;IAAE,GAPI,GAAEL,EAAG,IAAG+D,MAAM,CAAC/D,EAAE,IAAIgE,KAAM,EAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQnC,CACF,CAAC,EAGDnE,KAAK,iBACJJ,OAAA;MAAKO,KAAK,EAAE+B,UAAW;MAAAjC,QAAA,eACrBL,OAAA;QAAAK,QAAA,EAAOD;MAAK;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDvE,OAAA;MAAKO,KAAK,EAAE4C,YAAa;MAAA9C,QAAA,EACtBA;IAAQ;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAC,EAAA,GA3HavE,QAAQ;AA4HrB,OAAO,MAAMwE,YAAY,GAAGA,CAACvE,EAAE,EAAEiE,IAAI,EAAElC,QAAQ,EAAE1B,KAAK,GAAG,CAAC,CAAC,MAAM;EAC/DL,EAAE;EACFiE,IAAI;EACJlC,QAAQ;EACR1B;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMmE,cAAc,GAAG;EAC5BC,WAAW,EAAEA,CAACC,QAAQ,GAAG,QAAQ,KAAKH,YAAY,CAACG,QAAQ,EAAE,QAAQ,EAAE/E,QAAQ,CAACgF,KAAK,CAAC;EACtFC,UAAU,EAAEA,CAACF,QAAQ,GAAG,OAAO,KAAKH,YAAY,CAACG,QAAQ,EAAE,QAAQ,EAAE/E,QAAQ,CAACkF,IAAI,CAAC;EACnFC,SAAS,EAAEA,CAACJ,QAAQ,GAAG,OAAO,KAAKH,YAAY,CAACG,QAAQ,EAAE,QAAQ,EAAE/E,QAAQ,CAACoF,GAAG,CAAC;EACjFC,YAAY,EAAEA,CAACN,QAAQ,GAAG,QAAQ,KAAKH,YAAY,CAACG,QAAQ,EAAE,QAAQ,EAAE/E,QAAQ,CAACsF,MAAM;AACzF,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9B5C,QAAQ,EAAE,MAAM;EAChBb,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvB0B,GAAG,EAAE,KAAK;EAAE;EACZpB,KAAK,EAAE,SAAS;EAChBK,UAAU,EAAE,KAAK;EACjBE,YAAY,EAAE,MAAM;EAAE;EACtBhC,KAAK,EAAE,MAAM;EACb4B,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMgD,gBAAgB,GAAG;EAC9B7C,QAAQ,EAAE,MAAM;EAChBb,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBE,UAAU,EAAE,QAAQ;EACpBwB,GAAG,EAAE,KAAK;EACVpB,KAAK,EAAE,SAAS;EAChBK,UAAU,EAAE,KAAK;EACjBE,YAAY,EAAE,MAAM;EACpBhC,KAAK,EAAE,MAAM;EACb4B,SAAS,EAAE;AACb,CAAC;AAAC,IAAAmC,EAAA;AAAAc,YAAA,CAAAd,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}